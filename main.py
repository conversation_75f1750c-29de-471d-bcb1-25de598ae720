#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说助手 - 主程序入口
基于PySide6+Python开发的桌面应用程序，专为网络小说创作者设计
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QTranslator, QLocale
from PySide6.QtGui import QIcon

from ui.main_window import MainWindow
from config.settings import Settings


def setup_application():
    """设置应用程序基本信息"""
    app = QApplication(sys.argv)
    
    # 设置应用程序基本信息
    app.setApplicationName("AI小说助手")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AI Novel Assistant")
    app.setOrganizationDomain("ai-novel-assistant.com")
    
    # 设置应用程序图标
    icon_path = project_root / "data" / "icons" / "app_icon.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # 设置高DPI支持
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 设置中文本地化
    translator = QTranslator()
    locale = QLocale.system()
    if translator.load(locale, "qt", "_", str(project_root / "data" / "translations")):
        app.installTranslator(translator)
    
    return app


def main():
    """主函数"""
    try:
        # 创建应用程序
        app = setup_application()
        
        # 初始化设置
        settings = Settings()
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
