{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-01T17:27:39.484Z", "updatedAt": "2025-08-01T17:27:39.531Z", "resourceCount": 23}, "resources": [{"id": "ai-model-integration-expert", "source": "project", "protocol": "role", "name": "Ai Model Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-model-integration-expert/ai-model-integration-expert.role.md", "metadata": {"createdAt": "2025-08-01T17:27:39.487Z", "updatedAt": "2025-08-01T17:27:39.487Z", "scannedAt": "2025-08-01T17:27:39.487Z", "path": "role/ai-model-integration-expert/ai-model-integration-expert.role.md"}}, {"id": "ai-integration-workflow", "source": "project", "protocol": "execution", "name": "Ai Integration Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-model-integration-expert/execution/ai-integration-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T17:27:39.492Z", "updatedAt": "2025-08-01T17:27:39.492Z", "scannedAt": "2025-08-01T17:27:39.492Z", "path": "role/ai-model-integration-expert/execution/ai-integration-workflow.execution.md"}}, {"id": "ai-integration-thinking", "source": "project", "protocol": "thought", "name": "Ai Integration Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-model-integration-expert/thought/ai-integration-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T17:27:39.494Z", "updatedAt": "2025-08-01T17:27:39.494Z", "scannedAt": "2025-08-01T17:27:39.494Z", "path": "role/ai-model-integration-expert/thought/ai-integration-thinking.thought.md"}}, {"id": "doc-writer", "source": "project", "protocol": "role", "name": "Doc Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/doc-writer/doc-writer.role.md", "metadata": {"createdAt": "2025-08-01T17:27:39.495Z", "updatedAt": "2025-08-01T17:27:39.495Z", "scannedAt": "2025-08-01T17:27:39.495Z", "path": "role/doc-writer/doc-writer.role.md"}}, {"id": "document-standards", "source": "project", "protocol": "execution", "name": "Document Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/document-standards.execution.md", "metadata": {"createdAt": "2025-08-01T17:27:39.496Z", "updatedAt": "2025-08-01T17:27:39.496Z", "scannedAt": "2025-08-01T17:27:39.496Z", "path": "role/doc-writer/execution/document-standards.execution.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T17:27:39.497Z", "updatedAt": "2025-08-01T17:27:39.497Z", "scannedAt": "2025-08-01T17:27:39.497Z", "path": "role/doc-writer/execution/documentation-workflow.execution.md"}}, {"id": "document-types", "source": "project", "protocol": "thought", "name": "Document Types 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/document-types.thought.md", "metadata": {"createdAt": "2025-08-01T17:27:39.498Z", "updatedAt": "2025-08-01T17:27:39.499Z", "scannedAt": "2025-08-01T17:27:39.498Z", "path": "role/doc-writer/thought/document-types.thought.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T17:27:39.499Z", "updatedAt": "2025-08-01T17:27:39.499Z", "scannedAt": "2025-08-01T17:27:39.499Z", "path": "role/doc-writer/thought/documentation-thinking.thought.md"}}, {"id": "prompt-engineering-workflow", "source": "project", "protocol": "execution", "name": "Prompt Engineering Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/prompt-engineer/execution/prompt-engineering-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T17:27:39.501Z", "updatedAt": "2025-08-01T17:27:39.501Z", "scannedAt": "2025-08-01T17:27:39.501Z", "path": "role/prompt-engineer/execution/prompt-engineering-workflow.execution.md"}}, {"id": "prompt-engineer", "source": "project", "protocol": "role", "name": "Prompt Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/prompt-engineer/prompt-engineer.role.md", "metadata": {"createdAt": "2025-08-01T17:27:39.502Z", "updatedAt": "2025-08-01T17:27:39.502Z", "scannedAt": "2025-08-01T17:27:39.502Z", "path": "role/prompt-engineer/prompt-engineer.role.md"}}, {"id": "prompt-engineering-thinking", "source": "project", "protocol": "thought", "name": "Prompt Engineering Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/prompt-engineer/thought/prompt-engineering-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T17:27:39.508Z", "updatedAt": "2025-08-01T17:27:39.508Z", "scannedAt": "2025-08-01T17:27:39.508Z", "path": "role/prompt-engineer/thought/prompt-engineering-thinking.thought.md"}}, {"id": "desktop-development-workflow", "source": "project", "protocol": "execution", "name": "Desktop Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pyside6-desktop-developer/execution/desktop-development-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T17:27:39.511Z", "updatedAt": "2025-08-01T17:27:39.511Z", "scannedAt": "2025-08-01T17:27:39.511Z", "path": "role/pyside6-desktop-developer/execution/desktop-development-workflow.execution.md"}}, {"id": "pyside6-desktop-developer", "source": "project", "protocol": "role", "name": "Pyside6 Desktop Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pyside6-desktop-developer/pyside6-desktop-developer.role.md", "metadata": {"createdAt": "2025-08-01T17:27:39.512Z", "updatedAt": "2025-08-01T17:27:39.512Z", "scannedAt": "2025-08-01T17:27:39.512Z", "path": "role/pyside6-desktop-developer/pyside6-desktop-developer.role.md"}}, {"id": "desktop-development-thinking", "source": "project", "protocol": "thought", "name": "Desktop Development Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pyside6-desktop-developer/thought/desktop-development-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T17:27:39.514Z", "updatedAt": "2025-08-01T17:27:39.514Z", "scannedAt": "2025-08-01T17:27:39.514Z", "path": "role/pyside6-desktop-developer/thought/desktop-development-thinking.thought.md"}}, {"id": "project-coordination", "source": "project", "protocol": "execution", "name": "Project Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-coordination.execution.md", "metadata": {"createdAt": "2025-08-01T17:27:39.516Z", "updatedAt": "2025-08-01T17:27:39.516Z", "scannedAt": "2025-08-01T17:27:39.516Z", "path": "role/system-director/execution/project-coordination.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-08-01T17:27:39.517Z", "updatedAt": "2025-08-01T17:27:39.517Z", "scannedAt": "2025-08-01T17:27:39.517Z", "path": "role/system-director/system-director.role.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T17:27:39.519Z", "updatedAt": "2025-08-01T17:27:39.519Z", "scannedAt": "2025-08-01T17:27:39.519Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "ui-ux-design-workflow", "source": "project", "protocol": "execution", "name": "Ui Ux Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-ux-designer/execution/ui-ux-design-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T17:27:39.523Z", "updatedAt": "2025-08-01T17:27:39.523Z", "scannedAt": "2025-08-01T17:27:39.523Z", "path": "role/ui-ux-designer/execution/ui-ux-design-workflow.execution.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-ux-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T17:27:39.525Z", "updatedAt": "2025-08-01T17:27:39.525Z", "scannedAt": "2025-08-01T17:27:39.525Z", "path": "role/ui-ux-designer/thought/design-thinking.thought.md"}}, {"id": "ui-ux-designer", "source": "project", "protocol": "role", "name": "Ui Ux Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-ux-designer/ui-ux-designer.role.md", "metadata": {"createdAt": "2025-08-01T17:27:39.526Z", "updatedAt": "2025-08-01T17:27:39.526Z", "scannedAt": "2025-08-01T17:27:39.526Z", "path": "role/ui-ux-designer/ui-ux-designer.role.md"}}, {"id": "novel-creation-workflow", "source": "project", "protocol": "execution", "name": "Novel Creation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/web-novel-expert/execution/novel-creation-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T17:27:39.528Z", "updatedAt": "2025-08-01T17:27:39.528Z", "scannedAt": "2025-08-01T17:27:39.528Z", "path": "role/web-novel-expert/execution/novel-creation-workflow.execution.md"}}, {"id": "creative-writing-thinking", "source": "project", "protocol": "thought", "name": "Creative Writing Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/web-novel-expert/thought/creative-writing-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T17:27:39.530Z", "updatedAt": "2025-08-01T17:27:39.530Z", "scannedAt": "2025-08-01T17:27:39.529Z", "path": "role/web-novel-expert/thought/creative-writing-thinking.thought.md"}}, {"id": "web-novel-expert", "source": "project", "protocol": "role", "name": "Web Novel Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/web-novel-expert/web-novel-expert.role.md", "metadata": {"createdAt": "2025-08-01T17:27:39.530Z", "updatedAt": "2025-08-01T17:27:39.530Z", "scannedAt": "2025-08-01T17:27:39.530Z", "path": "role/web-novel-expert/web-novel-expert.role.md"}}], "stats": {"totalResources": 23, "byProtocol": {"role": 7, "execution": 8, "thought": 8}, "bySource": {"project": 23}}}